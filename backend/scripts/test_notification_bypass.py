#!/usr/bin/env python3
"""
Comprehensive Notification Test Script - Bypasses Timezone Restrictions

This script tests the complete notification pipeline by bypassing timezone checks
and sending test notifications immediately. It verifies:

1. Firebase push notification delivery mechanism
2. Notification formatting and sending
3. Both morning reminder and evening summary notifications
4. Individual and bulk notification functions
5. Real user token testing (with safety limits)

Usage:
    python3 test_notification_bypass.py [options]

Options:
    --dry-run           Show what would be sent without actually sending
    --max-users N       Limit to first N users (default: 3 for safety)
    --test-type TYPE    morning|evening|both|individual (default: both)
    --use-real-tokens   Use real user tokens from database
    --test-token TOKEN  Test with a specific FCM token

Safety Features:
    - Defaults to dry-run mode for safety
    - Limits to 3 users by default
    - Clear confirmation prompts for real notifications
    - Detailed logging of all operations
"""

import asyncio
import argparse
import json
import os
import sys
import logging
from datetime import datetime, time
from pathlib import Path
from typing import List, Tuple, Optional

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables from {env_path}")
except ImportError:
    print("ℹ python-dotenv not available, using system environment variables")

import firebase_admin
import pytz

# Import notification components
from utils.notifications import send_notification, send_bulk_notification
from database.notifications import get_users_token_in_timezones, get_users_id_in_timezones
from utils.llm.external_integrations import get_conversation_summary
from models.notification_message import NotificationMessage
from utils.webhooks import day_summary_webhook
import database.chat as chat_db
import database.conversations as conversations_db
import database.notifications as notification_db
from database._client import db
import threading


class NotificationTester:
    def __init__(self, dry_run=True, max_users=3, test_type="both", use_real_tokens=False, test_token=None):
        self.dry_run = dry_run
        self.max_users = max_users
        self.test_type = test_type
        self.use_real_tokens = use_real_tokens
        self.test_token = test_token
        self.firebase_initialized = False
        self.logger = self.setup_logging()
        
    def setup_logging(self):
        """Set up logging configuration"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('/tmp/notification_test.log')
            ]
        )
        return logging.getLogger(__name__)

    def initialize_firebase(self):
        """Initialize Firebase Admin SDK"""
        try:
            if firebase_admin._apps:
                self.logger.info("✓ Firebase already initialized")
                self.firebase_initialized = True
                return True
                
            service_account_json = os.environ.get('SERVICE_ACCOUNT_JSON')
            if service_account_json:
                service_account_info = json.loads(service_account_json)
                credentials = firebase_admin.credentials.Certificate(service_account_info)
                firebase_admin.initialize_app(credentials)
                self.logger.info("✓ Firebase initialized with SERVICE_ACCOUNT_JSON")
            else:
                firebase_admin.initialize_app()
                self.logger.info("✓ Firebase initialized with default credentials")
            
            self.firebase_initialized = True
            return True
            
        except Exception as e:
            self.logger.error(f"✗ Failed to initialize Firebase: {e}")
            return False

    def get_all_user_tokens_with_details(self) -> List[dict]:
        """Get all FCM tokens with user details from the database"""
        try:
            self.logger.info("🔍 Retrieving FCM tokens with user details...")
            users_ref = db.collection('users')
            tokens_data = []
            
            for doc in users_ref.stream():
                user_data = doc.to_dict()
                if 'fcm_token' in user_data and user_data['fcm_token']:
                    tokens_data.append({
                        'uid': doc.id,
                        'token': user_data['fcm_token'],
                        'time_zone': user_data.get('time_zone', 'Unknown'),
                        'email': user_data.get('email', 'Unknown'),
                        'created_at': user_data.get('created_at', 'Unknown')
                    })
            
            self.logger.info(f"📱 Found {len(tokens_data)} users with FCM tokens")
            return tokens_data[:self.max_users]  # Limit for safety
            
        except Exception as e:
            self.logger.error(f"❌ Error retrieving FCM tokens: {e}")
            return []

    def test_individual_notification(self, token: str, title: str, body: str, data: dict = None) -> bool:
        """Test sending a single notification"""
        self.logger.info(f"📤 Testing individual notification...")
        self.logger.info(f"   Token: {token[:20]}...")
        self.logger.info(f"   Title: {title}")
        self.logger.info(f"   Body: {body}")
        
        if self.dry_run:
            self.logger.info("🔄 DRY RUN - Notification would be sent but skipping actual delivery")
            return True
        
        try:
            result = send_notification(token, title, body, data)
            if result:
                self.logger.info("✅ Individual notification sent successfully")
            else:
                self.logger.error("❌ Individual notification failed")
            return result
        except Exception as e:
            self.logger.error(f"❌ Error sending individual notification: {e}")
            return False

    async def test_bulk_notification(self, tokens: List[str], title: str, body: str) -> bool:
        """Test sending bulk notifications"""
        self.logger.info(f"📤 Testing bulk notification to {len(tokens)} tokens...")
        self.logger.info(f"   Title: {title}")
        self.logger.info(f"   Body: {body}")
        
        if self.dry_run:
            self.logger.info("🔄 DRY RUN - Bulk notifications would be sent but skipping actual delivery")
            return True
        
        try:
            await send_bulk_notification(tokens, title, body)
            self.logger.info("✅ Bulk notifications sent successfully")
            return True
        except Exception as e:
            self.logger.error(f"❌ Error sending bulk notifications: {e}")
            return False

    def test_morning_notification(self, tokens_data: List[dict]) -> bool:
        """Test morning reminder notifications"""
        self.logger.info("\n🌅 === Testing Morning Notifications ===")
        
        title = "Memorion"
        body = "Wear your Memorion device to capture your conversations today."
        
        success_count = 0
        for user_data in tokens_data:
            self.logger.info(f"\n👤 Testing for user: {user_data['email']} (Timezone: {user_data['time_zone']})")
            
            if self.test_individual_notification(user_data['token'], title, body):
                success_count += 1
        
        self.logger.info(f"\n📊 Morning notification results: {success_count}/{len(tokens_data)} successful")
        return success_count > 0

    def test_evening_notification(self, tokens_data: List[dict]) -> bool:
        """Test evening summary notifications"""
        self.logger.info("\n🌙 === Testing Evening Summary Notifications ===")
        
        title = "Here is your action plan for tomorrow"
        success_count = 0
        
        for user_data in tokens_data:
            uid = user_data['uid']
            token = user_data['token']
            
            self.logger.info(f"\n👤 Testing for user: {user_data['email']} (Timezone: {user_data['time_zone']})")
            
            # Get user's conversations for summary (if any)
            try:
                memories = conversations_db.filter_conversations_by_date(
                    uid, datetime.combine(datetime.now().date(), time.min), datetime.now()
                )
                
                if memories:
                    self.logger.info(f"   Found {len(memories)} conversations for summary")
                    if not self.dry_run:
                        summary = get_conversation_summary(uid, memories)
                    else:
                        summary = "This is a test summary of your conversations today. You had meaningful discussions about work, family, and personal goals."
                else:
                    self.logger.info("   No conversations found, using default summary")
                    summary = "You had a quiet day today. Tomorrow is a new opportunity to capture meaningful conversations with your Memorion device."
                
            except Exception as e:
                self.logger.warning(f"   Could not get conversations for user {uid}: {e}")
                summary = "Your daily summary is ready. Check your Memorion app for insights from today's conversations."
            
            # Create notification message
            ai_message = NotificationMessage(
                text=summary,
                from_integration='false',
                type='day_summary',
                notification_type='daily_summary',
                navigate_to="/chat/omi",
            )
            
            # Send notification
            if self.test_individual_notification(token, title, summary, NotificationMessage.get_message_as_dict(ai_message)):
                success_count += 1
                
                # Add summary message to chat (optional for testing)
                if not self.dry_run:
                    try:
                        chat_db.add_summary_message(summary, uid)
                        self.logger.info("   ✓ Added summary message to chat")
                    except Exception as e:
                        self.logger.warning(f"   Could not add summary message for user {uid}: {e}")
                    
                    # Trigger webhook (optional for testing)
                    try:
                        threading.Thread(target=day_summary_webhook, args=(uid, summary)).start()
                        self.logger.info("   ✓ Triggered summary webhook")
                    except Exception as e:
                        self.logger.warning(f"   Could not trigger webhook for user {uid}: {e}")
        
        self.logger.info(f"\n📊 Evening notification results: {success_count}/{len(tokens_data)} successful")
        return success_count > 0

    async def test_bulk_notifications(self, tokens_data: List[dict]) -> bool:
        """Test bulk notification functionality"""
        self.logger.info("\n📦 === Testing Bulk Notifications ===")
        
        tokens = [user_data['token'] for user_data in tokens_data]
        
        # Test morning bulk notification
        morning_title = "Memorion"
        morning_body = "Wear your Memorion device to capture your conversations today."
        
        self.logger.info("🌅 Testing bulk morning notifications...")
        morning_success = await self.test_bulk_notification(tokens, morning_title, morning_body)
        
        # Test evening bulk notification
        evening_title = "Daily Summary Available"
        evening_body = "Your conversation insights are ready. Check your Memorion app for today's summary."
        
        self.logger.info("🌙 Testing bulk evening notifications...")
        evening_success = await self.test_bulk_notification(tokens, evening_title, evening_body)
        
        return morning_success and evening_success

    def confirm_real_notifications(self) -> bool:
        """Get user confirmation for sending real notifications"""
        if self.dry_run:
            return True

        print("\n" + "="*60)
        print("⚠️  WARNING: REAL NOTIFICATIONS WILL BE SENT")
        print("="*60)
        print(f"This will send REAL push notifications to {self.max_users} users.")
        print("These notifications will appear on their devices.")
        print("\nNotification types to be sent:")

        if self.test_type in ['morning', 'both']:
            print("  🌅 Morning reminder: 'Wear your Memorion device...'")
        if self.test_type in ['evening', 'both']:
            print("  🌙 Evening summary: Personalized conversation summaries")
        if self.test_type == 'individual':
            print("  📱 Individual test notifications")

        print(f"\nTarget users: {self.max_users}")
        print("="*60)

        response = input("Are you sure you want to send real notifications? (type 'YES' to confirm): ")
        return response == 'YES'

    async def run_tests(self) -> bool:
        """Run the complete test suite"""
        self.logger.info("🚀 Starting Comprehensive Notification Test Suite")
        self.logger.info("="*60)

        # Initialize Firebase
        if not self.initialize_firebase():
            return False

        # Get confirmation for real notifications
        if not self.confirm_real_notifications():
            self.logger.info("❌ Test cancelled by user")
            return False

        # Get user tokens
        if self.test_token:
            # Use specific token provided
            tokens_data = [{
                'uid': 'test_user',
                'token': self.test_token,
                'time_zone': 'Test',
                'email': '<EMAIL>',
                'created_at': 'Test'
            }]
            self.logger.info(f"🎯 Using provided test token: {self.test_token[:20]}...")
        elif self.use_real_tokens:
            # Get real tokens from database
            tokens_data = self.get_all_user_tokens_with_details()
            if not tokens_data:
                self.logger.error("❌ No user tokens found in database")
                return False
        else:
            # Use mock tokens for testing
            tokens_data = [{
                'uid': 'mock_user_1',
                'token': 'mock_token_1_for_testing_purposes_only',
                'time_zone': 'America/New_York',
                'email': '<EMAIL>',
                'created_at': 'Mock'
            }]
            self.logger.info("🎭 Using mock tokens for testing")

        self.logger.info(f"📱 Testing with {len(tokens_data)} user tokens")

        # Display user information
        self.logger.info("\n👥 Target Users:")
        for i, user_data in enumerate(tokens_data, 1):
            self.logger.info(f"  {i}. {user_data['email']} (Timezone: {user_data['time_zone']})")

        # Run tests based on test type
        all_tests_passed = True

        if self.test_type in ['morning', 'both']:
            if not self.test_morning_notification(tokens_data):
                all_tests_passed = False

        if self.test_type in ['evening', 'both']:
            if not self.test_evening_notification(tokens_data):
                all_tests_passed = False

        if self.test_type == 'individual':
            # Test individual notification function
            test_title = "Test Notification"
            test_body = "This is a test notification from the OMI system to verify push notification delivery."

            for user_data in tokens_data:
                if not self.test_individual_notification(user_data['token'], test_title, test_body):
                    all_tests_passed = False

        # Test bulk notifications if multiple users
        if len(tokens_data) > 1 and self.test_type == 'both':
            if not await self.test_bulk_notifications(tokens_data):
                all_tests_passed = False

        # Final results
        self.logger.info("\n" + "="*60)
        self.logger.info("📊 FINAL TEST RESULTS")
        self.logger.info("="*60)

        if all_tests_passed:
            self.logger.info("🎉 ALL TESTS PASSED!")
            self.logger.info("✅ Your notification system is working correctly")
            if not self.dry_run:
                self.logger.info("📱 Real notifications were sent successfully")
        else:
            self.logger.error("❌ SOME TESTS FAILED")
            self.logger.error("🔧 Please check the logs above for specific issues")

        self.logger.info(f"\n📝 Detailed logs saved to: /tmp/notification_test.log")

        return all_tests_passed


def main():
    """Main function to run the notification tests"""
    parser = argparse.ArgumentParser(description='Test OMI Notification System - Bypass Timezone Restrictions')
    parser.add_argument('--dry-run', action='store_true', default=True,
                       help='Show what would be sent without actually sending (default: True)')
    parser.add_argument('--send-real', action='store_true',
                       help='Actually send real notifications (overrides --dry-run)')
    parser.add_argument('--max-users', type=int, default=3,
                       help='Maximum number of users to test with (default: 3)')
    parser.add_argument('--test-type', choices=['morning', 'evening', 'both', 'individual'], default='both',
                       help='Type of notifications to test (default: both)')
    parser.add_argument('--use-real-tokens', action='store_true',
                       help='Use real user tokens from database')
    parser.add_argument('--test-token', type=str,
                       help='Test with a specific FCM token')

    args = parser.parse_args()

    # Override dry_run if send_real is specified
    dry_run = not args.send_real

    print("🧪 OMI Notification Test Suite - Bypass Timezone Restrictions")
    print("="*60)
    print(f"Mode: {'DRY RUN' if dry_run else 'REAL NOTIFICATIONS'}")
    print(f"Test Type: {args.test_type}")
    print(f"Max Users: {args.max_users}")
    print(f"Use Real Tokens: {args.use_real_tokens}")
    if args.test_token:
        print(f"Test Token: {args.test_token[:20]}...")
    print("="*60)

    # Create and run tester
    tester = NotificationTester(
        dry_run=dry_run,
        max_users=args.max_users,
        test_type=args.test_type,
        use_real_tokens=args.use_real_tokens,
        test_token=args.test_token
    )

    try:
        success = asyncio.run(tester.run_tests())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == "__main__":
    main()
