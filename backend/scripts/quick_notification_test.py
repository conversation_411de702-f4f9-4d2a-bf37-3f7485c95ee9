#!/usr/bin/env python3
"""
Quick Notification Test - Simple test for immediate verification

This script provides a quick way to test notifications without timezone restrictions.
Perfect for immediate verification of the notification pipeline.

Usage:
    python3 quick_notification_test.py [--real] [--token TOKEN]

Options:
    --real      Send real notifications (default: dry run)
    --token     Use specific FCM token for testing
"""

import asyncio
import argparse
import json
import os
import sys
from datetime import datetime
from pathlib import Path

# Add the backend directory to Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

# Load environment variables
try:
    from dotenv import load_dotenv
    env_path = backend_dir / '.env'
    if env_path.exists():
        load_dotenv(env_path)
        print(f"✓ Loaded environment variables")
except ImportError:
    print("ℹ Using system environment variables")

import firebase_admin
from utils.notifications import send_notification, send_bulk_notification
from database._client import db
from models.notification_message import NotificationMessage


def initialize_firebase():
    """Initialize Firebase Admin SDK"""
    try:
        if firebase_admin._apps:
            print("✓ Firebase already initialized")
            return True
            
        service_account_json = os.environ.get('SERVICE_ACCOUNT_JSON')
        if service_account_json:
            service_account_info = json.loads(service_account_json)
            credentials = firebase_admin.credentials.Certificate(service_account_info)
            firebase_admin.initialize_app(credentials)
            print("✓ Firebase initialized successfully")
        else:
            firebase_admin.initialize_app()
            print("✓ Firebase initialized with default credentials")
        
        return True
        
    except Exception as e:
        print(f"❌ Failed to initialize Firebase: {e}")
        return False


def get_sample_user_token():
    """Get a sample FCM token from the database"""
    try:
        print("🔍 Looking for user tokens in database...")
        users_ref = db.collection('users')
        
        for doc in users_ref.stream():
            user_data = doc.to_dict()
            if 'fcm_token' in user_data and user_data['fcm_token']:
                email = user_data.get('email', 'Unknown')
                timezone = user_data.get('time_zone', 'Unknown')
                print(f"📱 Found token for user: {email} (Timezone: {timezone})")
                return user_data['fcm_token'], doc.id, email
        
        print("❌ No FCM tokens found in database")
        return None, None, None
        
    except Exception as e:
        print(f"❌ Error retrieving token: {e}")
        return None, None, None


def test_morning_notification(token, dry_run=True):
    """Test morning reminder notification"""
    print("\n🌅 === Testing Morning Notification ===")
    
    title = "Memorion"
    body = "Wear your Memorion device to capture your conversations today."
    
    print(f"📤 Title: {title}")
    print(f"📤 Body: {body}")
    print(f"📤 Token: {token[:20]}...")
    
    if dry_run:
        print("🔄 DRY RUN - Would send notification but skipping actual delivery")
        return True
    
    try:
        result = send_notification(token, title, body)
        if result:
            print("✅ Morning notification sent successfully!")
        else:
            print("❌ Morning notification failed")
        return result
    except Exception as e:
        print(f"❌ Error sending morning notification: {e}")
        return False


def test_evening_notification(token, uid, dry_run=True):
    """Test evening summary notification"""
    print("\n🌙 === Testing Evening Notification ===")
    
    title = "Here is your action plan for tomorrow"
    body = "This is a test summary of your conversations today. You had meaningful discussions and captured important insights with your Memorion device."
    
    # Create notification message with proper data structure
    ai_message = NotificationMessage(
        text=body,
        from_integration='false',
        type='day_summary',
        notification_type='daily_summary',
        navigate_to="/chat/omi",
    )
    
    print(f"📤 Title: {title}")
    print(f"📤 Body: {body[:100]}...")
    print(f"📤 Token: {token[:20]}...")
    
    if dry_run:
        print("🔄 DRY RUN - Would send notification but skipping actual delivery")
        return True
    
    try:
        result = send_notification(token, title, body, NotificationMessage.get_message_as_dict(ai_message))
        if result:
            print("✅ Evening notification sent successfully!")
        else:
            print("❌ Evening notification failed")
        return result
    except Exception as e:
        print(f"❌ Error sending evening notification: {e}")
        return False


def test_custom_notification(token, dry_run=True):
    """Test custom notification"""
    print("\n📱 === Testing Custom Notification ===")
    
    title = "OMI Test Notification"
    body = f"This is a test notification sent at {datetime.now().strftime('%H:%M:%S')} to verify your notification system is working correctly."
    
    print(f"📤 Title: {title}")
    print(f"📤 Body: {body}")
    print(f"📤 Token: {token[:20]}...")
    
    if dry_run:
        print("🔄 DRY RUN - Would send notification but skipping actual delivery")
        return True
    
    try:
        result = send_notification(token, title, body)
        if result:
            print("✅ Custom notification sent successfully!")
        else:
            print("❌ Custom notification failed")
        return result
    except Exception as e:
        print(f"❌ Error sending custom notification: {e}")
        return False


async def test_bulk_notification(tokens, dry_run=True):
    """Test bulk notification"""
    print(f"\n📦 === Testing Bulk Notification ({len(tokens)} recipients) ===")
    
    title = "OMI Bulk Test"
    body = "This is a bulk test notification to verify the bulk notification system is working."
    
    print(f"📤 Title: {title}")
    print(f"📤 Body: {body}")
    print(f"📤 Recipients: {len(tokens)}")
    
    if dry_run:
        print("🔄 DRY RUN - Would send bulk notifications but skipping actual delivery")
        return True
    
    try:
        await send_bulk_notification(tokens, title, body)
        print("✅ Bulk notifications sent successfully!")
        return True
    except Exception as e:
        print(f"❌ Error sending bulk notifications: {e}")
        return False


async def main():
    """Main function"""
    parser = argparse.ArgumentParser(description='Quick Notification Test')
    parser.add_argument('--real', action='store_true', help='Send real notifications')
    parser.add_argument('--token', type=str, help='Use specific FCM token')
    
    args = parser.parse_args()
    dry_run = not args.real
    
    print("🧪 Quick Notification Test")
    print("=" * 50)
    print(f"Mode: {'REAL NOTIFICATIONS' if not dry_run else 'DRY RUN'}")
    print("=" * 50)
    
    # Initialize Firebase
    if not initialize_firebase():
        return False
    
    # Get token
    if args.token:
        token = args.token
        uid = "test_user"
        email = "<EMAIL>"
        print(f"🎯 Using provided token: {token[:20]}...")
    else:
        token, uid, email = get_sample_user_token()
        if not token:
            print("❌ No token available for testing")
            return False
    
    print(f"👤 Testing with user: {email}")
    
    # Confirm real notifications
    if not dry_run:
        print("\n⚠️  WARNING: This will send REAL notifications!")
        response = input("Are you sure? (type 'yes' to confirm): ")
        if response.lower() != 'yes':
            print("❌ Test cancelled")
            return False
    
    # Run tests
    success_count = 0
    total_tests = 3
    
    if test_morning_notification(token, dry_run):
        success_count += 1
    
    if test_evening_notification(token, uid, dry_run):
        success_count += 1
    
    if test_custom_notification(token, dry_run):
        success_count += 1
    
    # Test bulk if we have multiple tokens (only in real mode for now)
    if not dry_run and not args.token:
        # Get a few more tokens for bulk test
        try:
            users_ref = db.collection('users')
            tokens = []
            for doc in users_ref.stream():
                user_data = doc.to_dict()
                if 'fcm_token' in user_data and user_data['fcm_token']:
                    tokens.append(user_data['fcm_token'])
                    if len(tokens) >= 3:  # Limit to 3 for safety
                        break
            
            if len(tokens) > 1:
                if await test_bulk_notification(tokens, dry_run):
                    success_count += 1
                total_tests += 1
        except Exception as e:
            print(f"⚠️  Could not test bulk notifications: {e}")
    
    # Results
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS")
    print("=" * 50)
    print(f"✅ Passed: {success_count}/{total_tests}")
    
    if success_count == total_tests:
        print("🎉 ALL TESTS PASSED!")
        if not dry_run:
            print("📱 Real notifications were sent successfully")
        else:
            print("🔄 Dry run completed - notification system appears functional")
    else:
        print("❌ Some tests failed - check the output above")
    
    return success_count == total_tests


if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n❌ Test interrupted")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
